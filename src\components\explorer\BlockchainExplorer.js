import React, { useState, useEffect } from 'react';
import { Database, Layers, CreditCard, Activity, RefreshCw, Search, ChevronRight, ChevronDown, Clock, Hash, DollarSign, FileText } from 'lucide-react';
import web3Service from '../../services/Web3Service';

const BlockchainExplorer = () => {
  const [networkInfo, setNetworkInfo] = useState({
    id: null,
    name: '',
    blockNumber: 0,
    gasPrice: 0,
    accounts: [],
    isConnected: false
  });
  const [blocks, setBlocks] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedBlocks, setExpandedBlocks] = useState({});
  const [expandedTxs, setExpandedTxs] = useState({});
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);

  // Fonction pour charger les informations du réseau
  const loadNetworkInfo = async () => {
    try {
      setIsLoading(true);
      
      // Initialiser Web3Service si nécessaire
      if (!web3Service.initialized) {
        await web3Service.initialize(false);
      }
      
      const web3 = web3Service.web3;
      if (!web3) {
        throw new Error("Web3 n'est pas initialisé");
      }

      // Récupérer les informations du réseau
      const networkId = await web3.eth.net.getId();
      const networkName = web3Service.getNetworkName(networkId);
      const blockNumber = await web3.eth.getBlockNumber();
      const gasPrice = await web3.eth.getGasPrice();
      const accounts = await web3.eth.getAccounts();
      
      setNetworkInfo({
        id: networkId,
        name: networkName,
        blockNumber,
        gasPrice: web3.utils.fromWei(gasPrice, 'gwei'),
        accounts,
        isConnected: true
      });

      // Charger les blocs récents
      await loadRecentBlocks(blockNumber);
      
    } catch (error) {
      console.error("Erreur lors du chargement des informations du réseau:", error);
      setNetworkInfo({
        ...networkInfo,
        isConnected: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour charger les blocs récents
  const loadRecentBlocks = async (latestBlockNumber) => {
    try {
      const web3 = web3Service.web3;
      if (!web3) return;

      const blocksToFetch = 10; // Nombre de blocs à récupérer
      const blockPromises = [];
      const allTransactions = [];

      // Récupérer les 10 derniers blocs
      for (let i = 0; i < blocksToFetch && latestBlockNumber - i >= 0; i++) {
        blockPromises.push(web3.eth.getBlock(latestBlockNumber - i, true));
      }

      const fetchedBlocks = await Promise.all(blockPromises);
      
      // Extraire les transactions de tous les blocs
      fetchedBlocks.forEach(block => {
        if (block && block.transactions) {
          allTransactions.push(...block.transactions);
        }
      });

      setBlocks(fetchedBlocks);
      setTransactions(allTransactions.slice(0, 20)); // Limiter à 20 transactions pour les performances

    } catch (error) {
      console.error("Erreur lors du chargement des blocs récents:", error);
    }
  };

  // Fonction pour rechercher un bloc, une transaction ou un compte
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    try {
      setIsLoading(true);
      const web3 = web3Service.web3;
      
      // Vérifier si c'est un numéro de bloc
      if (/^\d+$/.test(searchQuery)) {
        const block = await web3.eth.getBlock(parseInt(searchQuery));
        if (block) {
          setSearchResults({ type: 'block', data: block });
          return;
        }
      }
      
      // Vérifier si c'est un hash de transaction
      if (/^0x[a-fA-F0-9]{64}$/.test(searchQuery)) {
        try {
          const tx = await web3.eth.getTransaction(searchQuery);
          if (tx) {
            setSearchResults({ type: 'transaction', data: tx });
            return;
          }
          
          // Si ce n'est pas une transaction, essayer comme un bloc
          const block = await web3.eth.getBlock(searchQuery);
          if (block) {
            setSearchResults({ type: 'block', data: block });
            return;
          }
        } catch (e) {
          console.log("Pas une transaction ou un bloc");
        }
      }
      
      // Vérifier si c'est une adresse
      if (/^0x[a-fA-F0-9]{40}$/.test(searchQuery)) {
        const balance = await web3.eth.getBalance(searchQuery);
        const txCount = await web3.eth.getTransactionCount(searchQuery);
        
        setSearchResults({
          type: 'account',
          data: {
            address: searchQuery,
            balance: web3.utils.fromWei(balance, 'ether'),
            txCount
          }
        });
        return;
      }
      
      // Aucun résultat trouvé
      setSearchResults({ type: 'notFound' });
      
    } catch (error) {
      console.error("Erreur lors de la recherche:", error);
      setSearchResults({ type: 'error', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  // Formater une adresse pour l'affichage
  const formatAddress = (address) => {
    if (!address) return "";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // Formater un timestamp en date lisible
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "";
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
  };

  // Basculer l'état d'expansion d'un bloc
  const toggleBlockExpansion = (blockNumber) => {
    setExpandedBlocks(prev => ({
      ...prev,
      [blockNumber]: !prev[blockNumber]
    }));
  };

  // Basculer l'état d'expansion d'une transaction
  const toggleTxExpansion = (txHash) => {
    setExpandedTxs(prev => ({
      ...prev,
      [txHash]: !prev[txHash]
    }));
  };

  // Charger les données au chargement du composant
  useEffect(() => {
    loadNetworkInfo();
    
    // Rafraîchir les données toutes les 15 secondes
    const intervalId = setInterval(() => {
      loadNetworkInfo();
    }, 15000);
    
    return () => clearInterval(intervalId);
  }, []);

  // Afficher un indicateur de chargement
  if (isLoading && !networkInfo.isConnected) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-[#2A3B8C] flex items-center">
        <Database className="mr-2" /> Explorateur Blockchain Ganache
      </h1>

      {/* Barre de recherche */}
      <div className="mb-6">
        <div className="flex">
          <input
            type="text"
            placeholder="Rechercher par adresse, hash de transaction ou numéro de bloc"
            className="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            className="bg-[#2A3B8C] text-white px-4 py-2 rounded-r-md hover:bg-[#1F2D6B] transition flex items-center"
            onClick={handleSearch}
          >
            <Search size={18} className="mr-1" /> Rechercher
          </button>
        </div>
      </div>

      {/* Affichage des résultats de recherche */}
      {searchResults && (
        <div className="mb-6 p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Résultats de recherche</h2>
          
          {searchResults.type === 'notFound' && (
            <div className="text-red-500">Aucun résultat trouvé pour cette recherche.</div>
          )}
          
          {searchResults.type === 'error' && (
            <div className="text-red-500">Erreur: {searchResults.message}</div>
          )}
          
          {searchResults.type === 'block' && (
            <div>
              <h3 className="text-lg font-medium mb-2">Bloc #{searchResults.data.number}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><span className="font-medium">Hash:</span> {searchResults.data.hash}</p>
                  <p><span className="font-medium">Hash parent:</span> {searchResults.data.parentHash}</p>
                  <p><span className="font-medium">Timestamp:</span> {formatTimestamp(searchResults.data.timestamp)}</p>
                </div>
                <div>
                  <p><span className="font-medium">Transactions:</span> {searchResults.data.transactions.length}</p>
                  <p><span className="font-medium">Gas utilisé:</span> {searchResults.data.gasUsed}</p>
                  <p><span className="font-medium">Mineur:</span> {formatAddress(searchResults.data.miner)}</p>
                </div>
              </div>
            </div>
          )}
          
          {searchResults.type === 'transaction' && (
            <div>
              <h3 className="text-lg font-medium mb-2">Transaction</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><span className="font-medium">Hash:</span> {searchResults.data.hash}</p>
                  <p><span className="font-medium">Bloc:</span> {searchResults.data.blockNumber}</p>
                  <p><span className="font-medium">De:</span> {formatAddress(searchResults.data.from)}</p>
                  <p><span className="font-medium">À:</span> {formatAddress(searchResults.data.to)}</p>
                </div>
                <div>
                  <p><span className="font-medium">Valeur:</span> {web3Service.web3.utils.fromWei(searchResults.data.value, 'ether')} ETH</p>
                  <p><span className="font-medium">Gas:</span> {searchResults.data.gas}</p>
                  <p><span className="font-medium">Gas Price:</span> {web3Service.web3.utils.fromWei(searchResults.data.gasPrice, 'gwei')} Gwei</p>
                  <p><span className="font-medium">Nonce:</span> {searchResults.data.nonce}</p>
                </div>
              </div>
            </div>
          )}
          
          {searchResults.type === 'account' && (
            <div>
              <h3 className="text-lg font-medium mb-2">Compte</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><span className="font-medium">Adresse:</span> {searchResults.data.address}</p>
                  <p><span className="font-medium">Solde:</span> {searchResults.data.balance} ETH</p>
                  <p><span className="font-medium">Nombre de transactions:</span> {searchResults.data.txCount}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Onglets */}
      <div className="mb-6">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            onClick={() => setActiveTab('overview')}
          >
            <Activity size={18} className="inline mr-1" /> Aperçu
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'blocks' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            onClick={() => setActiveTab('blocks')}
          >
            <Layers size={18} className="inline mr-1" /> Blocs
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'transactions' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            onClick={() => setActiveTab('transactions')}
          >
            <FileText size={18} className="inline mr-1" /> Transactions
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'accounts' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            onClick={() => setActiveTab('accounts')}
          >
            <CreditCard size={18} className="inline mr-1" /> Comptes
          </button>
        </div>
      </div>

      {/* Contenu des onglets */}
      <div className="bg-white rounded-lg shadow p-6">
        {/* Onglet Aperçu */}
        {activeTab === 'overview' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Informations du réseau</h2>
              <button
                onClick={loadNetworkInfo}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={16} className="mr-1" /> Rafraîchir
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800 mb-1">Réseau</h3>
                <p className="text-2xl font-bold">{networkInfo.name}</p>
                <p className="text-sm text-gray-600">ID: {networkInfo.id}</p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-green-800 mb-1">Dernier bloc</h3>
                <p className="text-2xl font-bold">{networkInfo.blockNumber}</p>
                <p className="text-sm text-gray-600">Hauteur actuelle</p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-800 mb-1">Prix du Gas</h3>
                <p className="text-2xl font-bold">{networkInfo.gasPrice} Gwei</p>
                <p className="text-sm text-gray-600">Prix moyen</p>
              </div>
              
              <div className="bg-amber-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-amber-800 mb-1">Comptes</h3>
                <p className="text-2xl font-bold">{networkInfo.accounts.length}</p>
                <p className="text-sm text-gray-600">Disponibles</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Derniers blocs</h3>
                <div className="border rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bloc</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transactions</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {blocks.slice(0, 5).map(block => (
                        <tr key={block.number} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm font-medium text-blue-600">{block.number}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{block.transactions.length}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{formatTimestamp(block.timestamp)}</div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-3">Dernières transactions</h3>
                <div className="border rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hash</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">De</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">À</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valeur</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {transactions.slice(0, 5).map(tx => (
                        <tr key={tx.hash} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm font-medium text-blue-600">{formatAddress(tx.hash)}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatAddress(tx.from)}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatAddress(tx.to)}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{web3Service.web3.utils.fromWei(tx.value, 'ether')} ETH</div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Onglet Blocs */}
        {activeTab === 'blocks' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Blocs récents</h2>
              <button
                onClick={loadNetworkInfo}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={16} className="mr-1" /> Rafraîchir
              </button>
            </div>
            
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bloc</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hash</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transactions</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gas utilisé</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Détails</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {blocks.map(block => (
                    <React.Fragment key={block.number}>
                      <tr className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">{block.number}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatAddress(block.hash)}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{block.transactions.length}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{block.gasUsed}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{formatTimestamp(block.timestamp)}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <button
                            onClick={() => toggleBlockExpansion(block.number)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            {expandedBlocks[block.number] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                          </button>
                        </td>
                      </tr>
                      {expandedBlocks[block.number] && (
                        <tr>
                          <td colSpan="6" className="px-4 py-3 bg-gray-50">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-2">
                              <div>
                                <p><span className="font-medium">Hash parent:</span> {block.parentHash}</p>
                                <p><span className="font-medium">Mineur:</span> {formatAddress(block.miner)}</p>
                                <p><span className="font-medium">Difficulté:</span> {block.difficulty}</p>
                              </div>
                              <div>
                                <p><span className="font-medium">Taille:</span> {block.size} octets</p>
                                <p><span className="font-medium">Gas Limit:</span> {block.gasLimit}</p>
                                <p><span className="font-medium">Nonce:</span> {block.nonce}</p>
                              </div>
                            </div>
                            {block.transactions.length > 0 && (
                              <div className="mt-3">
                                <h4 className="font-medium mb-2">Transactions dans ce bloc:</h4>
                                <ul className="text-sm">
                                  {block.transactions.slice(0, 5).map((tx, index) => (
                                    <li key={index} className="mb-1">
                                      <span className="text-blue-600">{typeof tx === 'string' ? tx : tx.hash}</span>
                                    </li>
                                  ))}
                                  {block.transactions.length > 5 && (
                                    <li className="text-gray-500">+ {block.transactions.length - 5} autres transactions</li>
                                  )}
                                </ul>
                              </div>
                            )}
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Onglet Transactions */}
        {activeTab === 'transactions' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Transactions récentes</h2>
              <button
                onClick={loadNetworkInfo}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={16} className="mr-1" /> Rafraîchir
              </button>
            </div>
            
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hash</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bloc</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">De</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">À</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valeur</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Détails</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map(tx => (
                    <React.Fragment key={tx.hash}>
                      <tr className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">{formatAddress(tx.hash)}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{tx.blockNumber}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatAddress(tx.from)}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatAddress(tx.to)}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{web3Service.web3.utils.fromWei(tx.value, 'ether')} ETH</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <button
                            onClick={() => toggleTxExpansion(tx.hash)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            {expandedTxs[tx.hash] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                          </button>
                        </td>
                      </tr>
                      {expandedTxs[tx.hash] && (
                        <tr>
                          <td colSpan="6" className="px-4 py-3 bg-gray-50">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-2">
                              <div>
                                <p><span className="font-medium">Hash:</span> {tx.hash}</p>
                                <p><span className="font-medium">Nonce:</span> {tx.nonce}</p>
                                <p><span className="font-medium">Index de transaction:</span> {tx.transactionIndex}</p>
                              </div>
                              <div>
                                <p><span className="font-medium">Gas:</span> {tx.gas}</p>
                                <p><span className="font-medium">Gas Price:</span> {web3Service.web3.utils.fromWei(tx.gasPrice, 'gwei')} Gwei</p>
                                <p><span className="font-medium">Input Data:</span> {tx.input && tx.input.length > 10 ? `${tx.input.substring(0, 10)}...` : tx.input}</p>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Onglet Comptes */}
        {activeTab === 'accounts' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Comptes disponibles</h2>
              <button
                onClick={loadNetworkInfo}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={16} className="mr-1" /> Rafraîchir
              </button>
            </div>
            
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Adresse</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Solde</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transactions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {networkInfo.accounts.map(async (account, index) => {
                    // Récupérer le solde pour chaque compte
                    const balance = await web3Service.web3.eth.getBalance(account);
                    const txCount = await web3Service.web3.eth.getTransactionCount(account);
                    
                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">{account}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{web3Service.web3.utils.fromWei(balance, 'ether')} ETH</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{txCount}</div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BlockchainExplorer;